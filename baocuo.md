Executing tasks: [:app:assembleDebug] in project C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1

Configuration on demand is an incubating feature.
> Task :app:preBuild UP-TO-DATE
> Task :app:preDebugBuild UP-TO-DATE
> Task :app:mergeDebugNativeDebugMetadata NO-SOURCE
> Task :app:checkKotlinGradlePluginConfigurationErrors
> Task :app:dataBindingMergeDependencyArtifactsDebug UP-TO-DATE
> Task :app:generateDebugResValues UP-TO-DATE
> Task :app:generateDebugResources
> Task :app:packageDebugResources
> Task :app:generateSafeArgsDebug UP-TO-DATE
> Task :app:checkDebugAarMetadata UP-TO-DATE
> Task :app:mapDebugSourceSetPaths
> Task :app:createDebugCompatibleScreenManifests UP-TO-DATE
> Task :app:extractDeepLinksDebug UP-TO-DATE
> Task :app:processDebugMainManifest UP-TO-DATE
> Task :app:processDebugManifest UP-TO-DATE
> Task :app:processDebugManifestForPackage UP-TO-DATE
> Task :app:javaPreCompileDebug UP-TO-DATE
> Task :app:mergeDebugShaders UP-TO-DATE
> Task :app:compileDebugShaders NO-SOURCE
> Task :app:generateDebugAssets UP-TO-DATE
> Task :app:mergeDebugAssets UP-TO-DATE
> Task :app:compressDebugAssets UP-TO-DATE
> Task :app:desugarDebugFileDependencies UP-TO-DATE
> Task :app:checkDebugDuplicateClasses UP-TO-DATE
> Task :app:mergeExtDexDebug UP-TO-DATE
> Task :app:mergeLibDexDebug UP-TO-DATE
> Task :app:mergeDebugJniLibFolders UP-TO-DATE
> Task :app:mergeDebugNativeLibs UP-TO-DATE
> Task :app:stripDebugDebugSymbols UP-TO-DATE
> Task :app:validateSigningDebug UP-TO-DATE
> Task :app:writeDebugAppMetadata UP-TO-DATE
> Task :app:writeDebugSigningConfigVersions UP-TO-DATE
> Task :app:parseDebugLocalResources
> Task :app:mergeDebugResources
> Task :app:dataBindingGenBaseClassesDebug
> Task :app:processDebugResources
> Task :app:kspDebugKotlin

> Task :app:compileDebugKotlin
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/service/UnifiedPlaybackService.kt:383:37 Parameter 'error' is never used
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/ui/login/LoginActivity.kt:71:40 'FLAG_FULLSCREEN: Int' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/ui/login/LoginActivity.kt:72:40 'FLAG_FULLSCREEN: Int' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/ui/login/LoginActivity.kt:77:30 'SYSTEM_UI_FLAG_HIDE_NAVIGATION: Int' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/ui/login/LoginActivity.kt:78:22 'SYSTEM_UI_FLAG_FULLSCREEN: Int' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/ui/login/LoginActivity.kt:79:22 'SYSTEM_UI_FLAG_IMMERSIVE_STICKY: Int' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/ui/login/LoginActivity.kt:80:22 'SYSTEM_UI_FLAG_LAYOUT_STABLE: Int' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/ui/login/LoginActivity.kt:81:22 'SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION: Int' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/ui/login/LoginActivity.kt:82:22 'SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN: Int' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/ui/login/LoginActivity.kt:83:19 'setter for systemUiVisibility: Int' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/ui/login/LoginActivity.kt:107:34 'SYSTEM_UI_FLAG_HIDE_NAVIGATION: Int' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/ui/login/LoginActivity.kt:108:26 'SYSTEM_UI_FLAG_FULLSCREEN: Int' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/ui/login/LoginActivity.kt:109:26 'SYSTEM_UI_FLAG_IMMERSIVE_STICKY: Int' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/ui/login/LoginActivity.kt:110:23 'setter for systemUiVisibility: Int' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/ui/login/LoginActivity.kt:122:13 Variable 'progressBar' is never used
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/ui/login/LoginActivity.kt:159:19 Enum argument can be null in Java, but exhaustive when contains no null branch
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/ui/login/LoginActivity.kt:555:13 'overridePendingTransition(Int, Int): Unit' is deprecated. Deprecated in Java
w: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/ui/login/LoginActivity.kt:586:13 Variable 'etCaptcha' is never used

> Task :app:compileDebugJavaWithJavac
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\adapter\PlaylistAdapter.java:17: 错误: 找不到符号
import com.example.aimusicplayer.utils.DiffCallbacks;
                                      ^
  符号:   类 DiffCallbacks
  位置: 程序包 com.example.aimusicplayer.utils
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\adapter\PlaylistAdapter.java:104: 错误: 程序包DiffCallbacks不存在
                new DiffCallbacks.PlaylistDiffCallback(this.playlists, newPlaylists));
                                 ^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\api\CookieInterceptor.java:43: 错误: 找不到符号
                        MusicApplication app = (MusicApplication) MusicApplication.getContext();
                                                                                  ^
  符号:   方法 getContext()
  位置: 类 MusicApplication
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\service\JavaPlayerControllerWrapper.java:112: 错误: 找不到符号
        PlayMode playMode = PlayMode.values()[mode % PlayMode.values().length];
                                    ^
  符号:   方法 values()
  位置: 类 PlayMode
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\service\JavaPlayerControllerWrapper.java:112: 错误: 找不到符号
        PlayMode playMode = PlayMode.values()[mode % PlayMode.values().length];
                                                             ^
  符号:   方法 values()
  位置: 类 PlayMode
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\service\JavaPlayerControllerWrapper.java:124: 错误: 找不到符号
            PlayMode[] modes = PlayMode.values();
                                       ^
  符号:   方法 values()
  位置: 类 PlayMode
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\service\JavaPlayerControllerWrapper.java:125: 错误: 找不到符号
            int currentIndex = currentMode.ordinal();
                                          ^
  符号:   方法 ordinal()
  位置: 类型为PlayMode的变量 currentMode
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\service\JavaPlayerControllerWrapper.java:137: 错误: 找不到符号
        return androidx.lifecycle.LiveDataReactiveStreams.fromPublisher(
                                 ^
  符号:   类 LiveDataReactiveStreams
  位置: 程序包 androidx.lifecycle
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\service\JavaPlayerControllerWrapper.java:138: 错误: 找不到符号
            kotlinx.coroutines.flow.FlowKt.asPublisher(playerController.getPlayState())
                                          ^
  符号:   方法 asPublisher(StateFlow<PlayState>)
  位置: 类 FlowKt
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\service\JavaPlayerControllerWrapper.java:148: 错误: 找不到符号
        return mode != null ? mode.ordinal() : 0;
                                  ^
  符号:   方法 ordinal()
  位置: 类型为PlayMode的变量 mode
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\service\JavaPlayerControllerWrapper.java:172: 错误: 对getPlaylist的引用不明确
        return playerController.getPlaylist();
                               ^
  PlayerController 中的方法 getPlaylist() 和 PlayerController 中的方法 getPlaylist() 都匹配
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\service\JavaPlayerControllerWrapper.java:172: 错误: 不兼容的类型: LiveData<List<MediaItem>>无法转换为List<MediaItem>
        return playerController.getPlaylist();
                                           ^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\ui\main\MainActivity.java:104: 错误: 无法将接口 Factory中的方法 from应用到给定类型;
            viewModel = new ViewModelProvider(this, ViewModelProvider.Factory.from(MainViewModel.class)).get(MainViewModel.class);
                                                                             ^
  需要: ViewModelInitializer<?>[]
  找到:    Class<MainViewModel>
  原因: varargs 不匹配; Class<MainViewModel>无法转换为ViewModelInitializer<?>
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\ui\main\MainActivity.java:179: 错误: 无法从静态上下文中引用非静态 方法 standardNavOptions()
                NavOptions navOptions = NavigationUtils.standardNavOptions();
                                                       ^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\ui\main\MainActivity.java:185: 错误: 找不到符号
                        setNavItemSelected((android.widget.ImageView)navPlayer, navPlayerIndicator);
                                                                     ^
  符号:   变量 navPlayer
  位置: 类 MainActivity
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\ui\main\MainActivity.java:190: 错误: 找不到符号
                        setNavItemSelected((android.widget.ImageView)navLibrary, navLibraryIndicator);
                                                                     ^
  符号:   变量 navLibrary
  位置: 类 MainActivity
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\ui\main\MainActivity.java:195: 错误: 找不到符号
                        setNavItemSelected((android.widget.ImageView)navDiscovery, navDiscoveryIndicator);
                                                                     ^
  符号:   变量 navDiscovery
  位置: 类 MainActivity
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\ui\main\MainActivity.java:200: 错误: 找不到符号
                        setNavItemSelected((android.widget.ImageView)navDriving, navDrivingIndicator);
                                                                     ^
  符号:   变量 navDriving
  位置: 类 MainActivity
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\ui\main\MainActivity.java:206: 错误: 找不到符号
                        setNavItemSelected((android.widget.ImageView)navProfile, navProfileIndicator);
                                                                     ^
  符号:   变量 navProfile
  位置: 类 MainActivity
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\ui\main\MainActivity.java:211: 错误: 找不到符号
                        setNavItemSelected((android.widget.ImageView)navSettings, navSettingsIndicator);
                                                                     ^
  符号:   变量 navSettings
  位置: 类 MainActivity
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\ui\main\MainActivity.java:269: 错误: 找不到符号
            ButtonAnimationUtils.addTouchFeedback(button);
                                ^
  符号:   方法 addTouchFeedback(View)
  位置: 类 ButtonAnimationUtils
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\ui\main\MainActivity.java:280: 错误: 不兼容的类型: View无法转换为LinearLayout
        viewModel.initializeSidebarController(sidebarNav, btnMenuRight, fragmentContainer, navButtons);
                                              ^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\ui\main\MainActivity.java:298: 错误: 无法取消引用void
            viewModel.checkLoginStatus().observe(this, loginStatus -> {
                                        ^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\ui\main\MainActivity.java:345: 错误: 无法取消引用void
        viewModel.getUserDetail(userId).observe(this, userDetail -> {
                                       ^
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\ui\main\MainActivity.java:398: 错误: 找不到符号
        ((android.widget.ImageView)navPlayer).setColorFilter(getResources().getColor(android.R.color.darker_gray));
                                   ^
  符号:   变量 navPlayer
  位置: 类 MainActivity
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\ui\main\MainActivity.java:399: 错误: 找不到符号
        ((android.widget.ImageView)navLibrary).setColorFilter(getResources().getColor(android.R.color.darker_gray));
                                   ^
  符号:   变量 navLibrary
  位置: 类 MainActivity
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\ui\main\MainActivity.java:400: 错误: 找不到符号
        ((android.widget.ImageView)navDiscovery).setColorFilter(getResources().getColor(android.R.color.darker_gray));
                                   ^
  符号:   变量 navDiscovery
  位置: 类 MainActivity
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\ui\main\MainActivity.java:401: 错误: 找不到符号
        ((android.widget.ImageView)navDriving).setColorFilter(getResources().getColor(android.R.color.darker_gray));
                                   ^
  符号:   变量 navDriving
  位置: 类 MainActivity
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\ui\main\MainActivity.java:402: 错误: 找不到符号
        ((android.widget.ImageView)navProfile).setColorFilter(getResources().getColor(android.R.color.darker_gray));
                                   ^
  符号:   变量 navProfile
  位置: 类 MainActivity
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\ui\main\MainActivity.java:403: 错误: 找不到符号
        ((android.widget.ImageView)navSettings).setColorFilter(getResources().getColor(android.R.color.darker_gray));
                                   ^
  符号:   变量 navSettings
  位置: 类 MainActivity
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\ui\splash\SplashActivity.java:108: 错误: 找不到符号
            viewModel.startSplashCountdown();
                     ^
  符号:   方法 startSplashCountdown()
  位置: 类型为SplashViewModel的变量 viewModel
C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\java\com\example\aimusicplayer\utils\LyricParser.java:101: 错误: 无法将类 LyricEntry中的构造器 LyricEntry应用到给定类型;
                    entries.add(new LyricEntry(time, text));
                                ^
  需要: long,String,String
  找到:    Long,String
  原因: 实际参数列表和形式参数列表长度不同
注: 某些输入文件使用或覆盖了已过时的 API。
注: 有关详细信息, 请使用 -Xlint:deprecation 重新编译。
注: 某些消息已经过简化; 请使用 -Xdiags:verbose 重新编译以获得完整输出
32 个错误

> Task :app:compileDebugJavaWithJavac FAILED

FAILURE: Build failed with an exception.

* What went wrong:
Execution failed for task ':app:compileDebugJavaWithJavac'.
> Compilation failed; see the compiler error output for details.

* Try:
> Run with --info option to get more log output.
> Run with --scan to get full insights.

BUILD FAILED in 21s
34 actionable tasks: 11 executed, 23 up-to-date
